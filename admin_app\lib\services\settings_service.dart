import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';

class SettingsService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _settingsCollection = 'app_settings';
  static const String _settingsDocId = 'global_settings';

  // Get approval settings
  static Future<Map<String, bool>> getApprovalSettings() async {
    try {
      final doc = await _firestore
          .collection(_settingsCollection)
          .doc(_settingsDocId)
          .get();

      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'require_user_approval': data['require_user_approval'] ?? true,
          'require_reseller_approval': data['require_reseller_approval'] ?? true,
        };
      } else {
        // Return default settings if document doesn't exist
        return {
          'require_user_approval': true,
          'require_reseller_approval': true,
        };
      }
    } catch (e) {
      print('Error getting approval settings: $e');
      // Return default settings on error
      return {
        'require_user_approval': true,
        'require_reseller_approval': true,
      };
    }
  }

  // Save approval settings
  static Future<void> saveApprovalSettings({
    required bool requireUserApproval,
    required bool requireResellerApproval,
  }) async {
    try {
      await _firestore
          .collection(_settingsCollection)
          .doc(_settingsDocId)
          .set({
        'require_user_approval': requireUserApproval,
        'require_reseller_approval': requireResellerApproval,
        'updated_at': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
    } catch (e) {
      throw Exception('Failed to save approval settings: ${e.toString()}');
    }
  }

  // Check if user approval is required
  static Future<bool> isUserApprovalRequired() async {
    try {
      final settings = await getApprovalSettings();
      return settings['require_user_approval'] ?? true;
    } catch (e) {
      print('Error checking user approval requirement: $e');
      return true; // Default to requiring approval on error
    }
  }

  // Check if reseller approval is required
  static Future<bool> isResellerApprovalRequired() async {
    try {
      final settings = await getApprovalSettings();
      return settings['require_reseller_approval'] ?? true;
    } catch (e) {
      print('Error checking reseller approval requirement: $e');
      return true; // Default to requiring approval on error
    }
  }

  // Initialize default settings (call this once during app setup)
  static Future<void> initializeDefaultSettings() async {
    try {
      final doc = await _firestore
          .collection(_settingsCollection)
          .doc(_settingsDocId)
          .get();

      if (!doc.exists) {
        await _firestore
            .collection(_settingsCollection)
            .doc(_settingsDocId)
            .set({
          'require_user_approval': true,
          'require_reseller_approval': true,
          'created_at': FieldValue.serverTimestamp(),
          'updated_at': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      print('Error initializing default settings: $e');
    }
  }

  // Listen to approval settings changes
  static Stream<Map<String, bool>> watchApprovalSettings() {
    return _firestore
        .collection(_settingsCollection)
        .doc(_settingsDocId)
        .snapshots()
        .map((doc) {
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'require_user_approval': data['require_user_approval'] ?? true,
          'require_reseller_approval': data['require_reseller_approval'] ?? true,
        };
      } else {
        return {
          'require_user_approval': true,
          'require_reseller_approval': true,
        };
      }
    });
  }

  // Fix existing users registration status (one-time migration)
  static Future<void> fixExistingUsersRegistrationStatus() async {
    try {
      final settings = await getApprovalSettings();
      final requiresApproval = settings['require_user_approval'] ?? true;

      if (!requiresApproval) {
        // If approval is not required, update all pending users to approved
        final pendingUsersQuery = await _firestore
            .collection(AppConstants.usersCollection)
            .where('registrationStatus', isEqualTo: 'pending')
            .get();

        final batch = _firestore.batch();

        for (final doc in pendingUsersQuery.docs) {
          batch.update(doc.reference, {
            'registrationStatus': 'approved',
            'updatedAt': FieldValue.serverTimestamp(),
          });
        }

        await batch.commit();
        print('Fixed ${pendingUsersQuery.docs.length} users registration status');
      }
    } catch (e) {
      print('Error fixing existing users registration status: $e');
    }
  }
}
