import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../enums/user_role.dart';
import '../services/user_management_service.dart';
import 'user_detail_screen.dart';

class ResellerManagementScreen extends StatefulWidget {
  const ResellerManagementScreen({super.key});

  @override
  State<ResellerManagementScreen> createState() => _ResellerManagementScreenState();
}

class _ResellerManagementScreenState extends State<ResellerManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<UserModel> _resellers = [];
  List<UserModel> _filteredResellers = [];
  bool _isLoading = true;
  bool? _selectedActiveStatus;

  @override
  void initState() {
    super.initState();
    _loadResellers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: Column(
        children: [
          _buildHeader(),
          _buildFilters(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildResellersList(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.verified_user,
            size: 32,
            color: AppConstants.primaryColor,
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Reseller Management',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Manage all resellers and their activities',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppConstants.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '${_filteredResellers.length} Resellers',
              style: TextStyle(
                color: AppConstants.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'Search resellers...',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (_) => _filterResellers(),
                  ),
                ),
                const SizedBox(width: 16),
                DropdownButton<bool?>(
                  value: _selectedActiveStatus,
                  hint: const Text('Filter by Status'),
                  items: const [
                    DropdownMenuItem(value: null, child: Text('All Status')),
                    DropdownMenuItem(value: true, child: Text('Active')),
                    DropdownMenuItem(value: false, child: Text('Inactive')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedActiveStatus = value;
                    });
                    _loadResellers();
                  },
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _loadResellers,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _loadResellers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final resellers = await UserManagementService.getUsers(
        limit: 100,
        roleFilter: UserRole.reseller,
        isActiveFilter: _selectedActiveStatus,
      );

      setState(() {
        _resellers = resellers;
        _filteredResellers = resellers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading resellers: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  void _filterResellers() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredResellers = _resellers.where((reseller) {
        final matchesSearch = query.isEmpty ||
            reseller.username.toLowerCase().contains(query) ||
            reseller.email.toLowerCase().contains(query) ||
            reseller.displayName.toLowerCase().contains(query);

        return matchesSearch;
      }).toList();
    });
  }

  Widget _buildResellersList() {
    if (_filteredResellers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.verified_user_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No resellers found',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search criteria',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      itemCount: _filteredResellers.length,
      itemBuilder: (context, index) {
        final reseller = _filteredResellers[index];
        return _buildResellerCard(reseller);
      },
    );
  }

  Widget _buildResellerCard(UserModel reseller) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundImage: (reseller.profileImageUrl?.isNotEmpty ?? false)
              ? NetworkImage(reseller.profileImageUrl!)
              : null,
          child: (reseller.profileImageUrl?.isEmpty ?? true)
              ? Text(reseller.displayName.isNotEmpty
                  ? reseller.displayName[0].toUpperCase()
                  : reseller.username[0].toUpperCase())
              : null,
        ),
        title: Text(
          reseller.displayName.isNotEmpty ? reseller.displayName : reseller.username,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(reseller.email),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: reseller.isActive ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    reseller.isActive ? 'Active' : 'Inactive',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                if (reseller.isVerified)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Verified',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.visibility),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => UserDetailScreen(user: reseller),
                  ),
                );
              },
              tooltip: 'View Details',
            ),
            PopupMenuButton<String>(
              onSelected: (action) => _handleResellerAction(reseller, action),
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: reseller.isActive ? 'deactivate' : 'activate',
                  child: Text(reseller.isActive ? 'Deactivate' : 'Activate'),
                ),
                PopupMenuItem(
                  value: reseller.isVerified ? 'unverify' : 'verify',
                  child: Text(reseller.isVerified ? 'Unverify' : 'Verify'),
                ),
                const PopupMenuItem(
                  value: 'reset_password',
                  child: Text('Reset Password'),
                ),
                const PopupMenuItem(
                  value: 'change_role',
                  child: Text('Change Role'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _handleResellerAction(UserModel reseller, String action) async {
    try {
      switch (action) {
        case 'activate':
        case 'deactivate':
          final isActive = action == 'activate';
          await UserManagementService.toggleUserActiveStatus(reseller.id, isActive);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Reseller ${isActive ? 'activated' : 'deactivated'} successfully'),
              backgroundColor: AppConstants.successColor,
            ),
          );
          _loadResellers();
          break;
        case 'verify':
        case 'unverify':
          final isVerified = action == 'verify';
          await UserManagementService.toggleUserVerifiedStatus(reseller.id, isVerified);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Reseller ${isVerified ? 'verified' : 'unverified'} successfully'),
              backgroundColor: AppConstants.successColor,
            ),
          );
          _loadResellers();
          break;
        case 'reset_password':
          await UserManagementService.sendPasswordResetEmail(reseller.email);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Password reset email sent to ${reseller.email}'),
              backgroundColor: AppConstants.successColor,
            ),
          );
          break;
        case 'change_role':
          // Navigate to user detail screen for role change
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => UserDetailScreen(user: reseller),
            ),
          );
          break;
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }
}
