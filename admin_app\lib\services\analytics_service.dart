import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/analytics_model.dart';
import '../models/user_model.dart';
import '../models/post_model.dart';
import '../models/product_model.dart';
import '../models/comment_model.dart';
import '../constants/app_constants.dart';
import '../enums/user_role.dart';

class AnalyticsService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get comprehensive analytics data
  static Future<AnalyticsModel> getAnalytics() async {
    try {
      final DateTime now = DateTime.now();
      final DateTime todayStart = DateTime(now.year, now.month, now.day);
      final DateTime weekAgo = now.subtract(const Duration(days: 7));

      // Get all collections data in parallel
      final futures = await Future.wait([
        _getUserStats(todayStart),
        _getPostStats(todayStart),
        _getProductStats(todayStart),
        _getCommentStats(todayStart),
        _getDailyStats(weekAgo, now),
      ]);

      final userStats = futures[0] as Map<String, dynamic>;
      final postStats = futures[1] as Map<String, dynamic>;
      final productStats = futures[2] as Map<String, dynamic>;
      final commentStats = futures[3] as Map<String, dynamic>;
      final dailyStats = futures[4] as List<DailyStats>;

      return AnalyticsModel(
        totalUsers: userStats['total'],
        totalResellers: userStats['resellers'],
        totalAdmins: userStats['admins'],
        totalPosts: postStats['total'],
        totalProducts: productStats['total'],
        totalComments: commentStats['total'],
        activeUsers: userStats['active'],
        pendingResellerApplications: userStats['pendingApplications'],
        newUsersToday: userStats['newToday'],
        newPostsToday: postStats['newToday'],
        newProductsToday: productStats['newToday'],
        totalRevenue: productStats['totalRevenue'],
        usersByCountry: userStats['byCountry'],
        postsByCategory: postStats['byCategory'],
        productsByCategory: productStats['byCategory'],
        dailyStats: dailyStats,
        lastUpdated: now,
      );
    } catch (e) {
      throw Exception('Failed to get analytics: ${e.toString()}');
    }
  }

  // Get user statistics
  static Future<Map<String, dynamic>> _getUserStats(DateTime todayStart) async {
    final QuerySnapshot allUsers = await _firestore
        .collection(AppConstants.usersCollection)
        .get();

    int total = 0;
    int resellers = 0;
    int admins = 0;
    int active = 0;
    int pendingApplications = 0;
    int newToday = 0;
    Map<String, int> byCountry = {};

    for (final doc in allUsers.docs) {
      final user = UserModel.fromDocument(doc);
      total++;

      if (user.isActive) active++;
      if (user.isReseller) resellers++;
      if (user.isAdmin) admins++;
      if (user.hasPendingResellerApplication) pendingApplications++;
      if (user.createdAt.isAfter(todayStart)) newToday++;

      // Count by country
      final country = user.country ?? 'Unknown';
      byCountry[country] = (byCountry[country] ?? 0) + 1;
    }

    return {
      'total': total,
      'resellers': resellers,
      'admins': admins,
      'active': active,
      'pendingApplications': pendingApplications,
      'newToday': newToday,
      'byCountry': byCountry,
    };
  }

  // Get post statistics
  static Future<Map<String, dynamic>> _getPostStats(DateTime todayStart) async {
    final QuerySnapshot allPosts = await _firestore
        .collection(AppConstants.postsCollection)
        .get();

    int total = 0;
    int newToday = 0;
    Map<String, int> byCategory = {};

    for (final doc in allPosts.docs) {
      final post = PostModel.fromDocument(doc);
      if (post.isActive) {
        total++;
        if (post.createdAt.isAfter(todayStart)) newToday++;

        // Categorize posts by content type
        String category = 'Text';
        if (post.hasImages) category = 'Image';
        
        byCategory[category] = (byCategory[category] ?? 0) + 1;
      }
    }

    return {
      'total': total,
      'newToday': newToday,
      'byCategory': byCategory,
    };
  }

  // Get product statistics
  static Future<Map<String, dynamic>> _getProductStats(DateTime todayStart) async {
    final QuerySnapshot allProducts = await _firestore
        .collection(AppConstants.productsCollection)
        .get();

    int total = 0;
    int newToday = 0;
    double totalRevenue = 0.0;
    Map<String, int> byCategory = {};

    for (final doc in allProducts.docs) {
      final product = ProductModel.fromDocument(doc);
      if (product.isAvailable) {
        total++;
        if (product.createdAt.isAfter(todayStart)) newToday++;
        
        // Calculate estimated revenue (this is a placeholder - in real app you'd have sales data)
        totalRevenue += product.price * 0.1; // Assume 10% of price as commission

        // Count by category
        byCategory[product.category] = (byCategory[product.category] ?? 0) + 1;
      }
    }

    return {
      'total': total,
      'newToday': newToday,
      'totalRevenue': totalRevenue,
      'byCategory': byCategory,
    };
  }

  // Get comment statistics
  static Future<Map<String, dynamic>> _getCommentStats(DateTime todayStart) async {
    final QuerySnapshot allComments = await _firestore
        .collection(AppConstants.commentsCollection)
        .get();

    int total = 0;
    int newToday = 0;

    for (final doc in allComments.docs) {
      final comment = CommentModel.fromDocument(doc);
      if (comment.isActive) {
        total++;
        if (comment.createdAt.isAfter(todayStart)) newToday++;
      }
    }

    return {
      'total': total,
      'newToday': newToday,
    };
  }

  // Get daily statistics for the past week
  static Future<List<DailyStats>> _getDailyStats(DateTime startDate, DateTime endDate) async {
    final List<DailyStats> dailyStats = [];

    for (DateTime date = startDate; date.isBefore(endDate); date = date.add(const Duration(days: 1))) {
      final dayStart = DateTime(date.year, date.month, date.day);
      final dayEnd = dayStart.add(const Duration(days: 1));

      // Get counts for this day
      final futures = await Future.wait([
        _getCountForDay(AppConstants.usersCollection, dayStart, dayEnd),
        _getCountForDay(AppConstants.postsCollection, dayStart, dayEnd),
        _getCountForDay(AppConstants.productsCollection, dayStart, dayEnd),
        _getCountForDay(AppConstants.commentsCollection, dayStart, dayEnd),
      ]);

      dailyStats.add(DailyStats(
        date: dayStart,
        newUsers: futures[0],
        newPosts: futures[1],
        newProducts: futures[2],
        newComments: futures[3],
        revenue: futures[2] * 10.0, // Placeholder revenue calculation
      ));
    }

    return dailyStats;
  }

  // Get count of documents created in a specific day
  static Future<int> _getCountForDay(String collection, DateTime dayStart, DateTime dayEnd) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection(collection)
          .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(dayStart))
          .where('createdAt', isLessThan: Timestamp.fromDate(dayEnd))
          .get();

      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  // Get real-time user count
  static Stream<int> getUserCountStream() {
    return _firestore
        .collection(AppConstants.usersCollection)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  // Get real-time post count
  static Stream<int> getPostCountStream() {
    return _firestore
        .collection(AppConstants.postsCollection)
        .where('isActive', isEqualTo: true)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  // Get real-time product count
  static Stream<int> getProductCountStream() {
    return _firestore
        .collection(AppConstants.productsCollection)
        .where('isAvailable', isEqualTo: true)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  // Get real-time reseller count
  static Stream<int> getResellerCountStream() {
    return _firestore
        .collection(AppConstants.usersCollection)
        .where('role', isEqualTo: UserRole.reseller.value)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  // Get pending reseller applications count
  static Stream<int> getPendingApplicationsCountStream() {
    return _firestore
        .collection(AppConstants.usersCollection)
        .where('resellerApplicationStatus', isEqualTo: ResellerApplicationStatus.pending.value)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  // Get pending user registrations count
  static Stream<int> getPendingRegistrationsCountStream() {
    return _firestore
        .collection(AppConstants.usersCollection)
        .where('registrationStatus', isEqualTo: 'pending')
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }
}
