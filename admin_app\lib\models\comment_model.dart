import 'package:cloud_firestore/cloud_firestore.dart';

class CommentModel {
  final String id;
  final String postId;
  final String userId;
  final String username;
  final String userDisplayName;
  final String? userProfileImageUrl;
  final String content;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> likes;
  final bool isActive;
  final bool isApproved;
  final bool isDeleted;
  final String? moderatorNote;
  final DateTime? deletedAt;

  CommentModel({
    required this.id,
    required this.postId,
    required this.userId,
    required this.username,
    required this.userDisplayName,
    this.userProfileImageUrl,
    required this.content,
    required this.createdAt,
    required this.updatedAt,
    this.likes = const [],
    this.isActive = true,
    this.isApproved = true,
    this.isDeleted = false,
    this.moderatorNote,
    this.deletedAt,
  });

  // Convert CommentModel to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'postId': postId,
      'userId': userId,
      'username': username,
      'userDisplayName': userDisplayName,
      'userProfileImageUrl': userProfileImageUrl,
      'content': content,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'likes': likes,
      'isActive': isActive,
      'isApproved': isApproved,
      'isDeleted': isDeleted,
      'moderatorNote': moderatorNote,
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
    };
  }

  // Create CommentModel from Firestore document
  factory CommentModel.fromMap(Map<String, dynamic> map) {
    return CommentModel(
      id: map['id'] ?? '',
      postId: map['postId'] ?? '',
      userId: map['userId'] ?? '',
      username: map['username'] ?? '',
      userDisplayName: map['userDisplayName'] ?? '',
      userProfileImageUrl: map['userProfileImageUrl'],
      content: map['content'] ?? '',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      likes: List<String>.from(map['likes'] ?? []),
      isActive: map['isActive'] ?? true,
      isApproved: map['isApproved'] ?? true,
      isDeleted: map['isDeleted'] ?? false,
      moderatorNote: map['moderatorNote'],
      deletedAt: (map['deletedAt'] as Timestamp?)?.toDate(),
    );
  }

  // Create CommentModel from Firestore DocumentSnapshot
  factory CommentModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return CommentModel.fromMap(data);
  }

  // Create a copy of CommentModel with updated fields
  CommentModel copyWith({
    String? id,
    String? postId,
    String? userId,
    String? username,
    String? userDisplayName,
    String? userProfileImageUrl,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? likes,
    bool? isActive,
    bool? isApproved,
    bool? isDeleted,
    String? moderatorNote,
    DateTime? deletedAt,
  }) {
    return CommentModel(
      id: id ?? this.id,
      postId: postId ?? this.postId,
      userId: userId ?? this.userId,
      username: username ?? this.username,
      userDisplayName: userDisplayName ?? this.userDisplayName,
      userProfileImageUrl: userProfileImageUrl ?? this.userProfileImageUrl,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      likes: likes ?? this.likes,
      isActive: isActive ?? this.isActive,
      isApproved: isApproved ?? this.isApproved,
      isDeleted: isDeleted ?? this.isDeleted,
      moderatorNote: moderatorNote ?? this.moderatorNote,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }

  // Helper getters
  int get likeCount => likes.length;

  // Check if user liked the comment
  bool isLikedBy(String userId) => likes.contains(userId);

  // Get formatted time ago
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return '${years}y ago';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return '${months}mo ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  // Get formatted date
  String get formattedDate {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[createdAt.month - 1]} ${createdAt.day}, ${createdAt.year}';
  }

  // Get content preview (first 50 characters)
  String get contentPreview {
    if (content.length <= 50) return content;
    return '${content.substring(0, 50)}...';
  }

  @override
  String toString() {
    return 'CommentModel(id: $id, postId: $postId, userId: $userId, content: ${contentPreview})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommentModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Compatibility getters for content moderation screen
  String get authorName => userDisplayName.isNotEmpty ? userDisplayName : username;
  String? get authorProfileImageUrl => userProfileImageUrl;
}
