{"indexes": [{"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "resellerApplicationStatus", "order": "ASCENDING"}, {"fieldPath": "resellerApplicationDate", "order": "DESCENDING"}]}, {"collectionGroup": "debug_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "debug_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "level", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "debug_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}], "fieldOverrides": []}