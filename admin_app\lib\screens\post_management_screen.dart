import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';
import '../models/post_model.dart';
import '../services/post_service.dart';
import '../widgets/post_management/post_card_admin.dart';
import '../widgets/common/loading_widget.dart';
import '../widgets/common/empty_state_widget.dart';

class PostManagementScreen extends StatefulWidget {
  const PostManagementScreen({super.key});

  @override
  State<PostManagementScreen> createState() => _PostManagementScreenState();
}

class _PostManagementScreenState extends State<PostManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  // Data
  List<PostModel> _posts = [];
  bool _isLoading = false;
  bool _hasMorePosts = true;
  DocumentSnapshot? _lastDocument;
  String _currentFilter = 'all';
  String _searchQuery = '';

  // Statistics
  Map<String, int> _statistics = {
    'total': 0,
    'active': 0,
    'pending': 0,
    'inactive': 0,
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_onTabChanged);
    _scrollController.addListener(_onScroll);
    _loadStatistics();
    _loadPosts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      final filters = ['all', 'active', 'pending', 'inactive'];
      _currentFilter = filters[_tabController.index];
      _refreshPosts();
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMorePosts();
    }
  }

  Future<void> _loadStatistics() async {
    try {
      final stats = await PostService.getPostsStatistics();
      if (mounted) {
        setState(() {
          _statistics = stats;
        });
      }
    } catch (e) {
      print('Error loading statistics: $e');
    }
  }

  Future<void> _loadPosts() async {
    if (_isLoading || !_hasMorePosts) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final posts = await PostService.getAllPostsPaginated(
        lastDocument: _lastDocument,
        status: _currentFilter,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
      );

      if (mounted) {
        setState(() {
          if (_lastDocument == null) {
            _posts = posts;
          } else {
            _posts.addAll(posts);
          }
          _hasMorePosts = posts.length >= 20;
          if (posts.isNotEmpty) {
            // This would need to be implemented in PostService
            // _lastDocument = posts.last.lastDocument;
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      print('Error loading posts: $e');
    }
  }

  Future<void> _loadMorePosts() async {
    if (!_isLoading && _hasMorePosts) {
      await _loadPosts();
    }
  }

  void _refreshPosts() {
    setState(() {
      _posts.clear();
      _lastDocument = null;
      _hasMorePosts = true;
    });
    _loadPosts();
    _loadStatistics();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _refreshPosts();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          _buildSearchBar(),
          Expanded(
            child: _buildPostsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(
            color: AppConstants.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.article_outlined,
            size: AppConstants.iconSizeLarge,
            color: AppConstants.primaryColor,
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          const Text(
            'Post Management',
            style: TextStyle(
              fontSize: AppConstants.fontSizeHeading,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const Spacer(),
          // Comments button
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pushNamed(context, '/comments');
            },
            icon: const Icon(Icons.comment, size: 18),
            label: const Text('Comments'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade50,
              foregroundColor: Colors.blue.shade700,
              elevation: 0,
            ),
          ),
          const SizedBox(width: 8),
          // Reports button
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pushNamed(context, '/reports');
            },
            icon: const Icon(Icons.report, size: 18),
            label: const Text('Reports'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade50,
              foregroundColor: Colors.orange.shade700,
              elevation: 0,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: _refreshPosts,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppConstants.surfaceColor,
      child: TabBar(
        controller: _tabController,
        labelColor: AppConstants.primaryColor,
        unselectedLabelColor: AppConstants.textSecondaryColor,
        indicatorColor: AppConstants.primaryColor,
        tabs: [
          Tab(
            text: 'All (${_statistics['total']})',
          ),
          Tab(
            text: 'Active (${_statistics['active']})',
          ),
          Tab(
            text: 'Pending (${_statistics['pending']})',
          ),
          Tab(
            text: 'Inactive (${_statistics['inactive']})',
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      color: AppConstants.surfaceColor,
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search posts by content, username, or display name...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    _onSearchChanged('');
                  },
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            borderSide: const BorderSide(color: AppConstants.borderColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            borderSide: const BorderSide(color: AppConstants.primaryColor),
          ),
        ),
        onChanged: _onSearchChanged,
      ),
    );
  }

  Widget _buildPostsList() {
    if (_isLoading && _posts.isEmpty) {
      return const LoadingWidget();
    }

    if (_posts.isEmpty && !_isLoading) {
      return EmptyStateWidget(
        icon: Icons.article_outlined,
        title: 'No Posts Found',
        subtitle: _searchQuery.isNotEmpty
            ? 'No posts match your search criteria'
            : 'No posts available for the selected filter',
        actionText: 'Refresh',
        onAction: _refreshPosts,
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _refreshPosts();
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _posts.length + (_hasMorePosts ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= _posts.length) {
            return const Padding(
              padding: EdgeInsets.all(AppConstants.paddingMedium),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          return Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
            child: PostCardAdmin(
              post: _posts[index],
              onPostUpdated: () {
                _refreshPosts();
              },
            ),
          );
        },
      ),
    );
  }
}
