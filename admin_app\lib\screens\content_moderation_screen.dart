import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../constants/app_constants.dart';
import '../models/post_model.dart';
import '../models/comment_model.dart';
import '../services/post_service.dart';
import '../services/comment_service.dart';
import 'main_layout.dart';

class ContentModerationScreen extends StatefulWidget {
  const ContentModerationScreen({super.key});

  @override
  State<ContentModerationScreen> createState() => _ContentModerationScreenState();
}

class _ContentModerationScreenState extends State<ContentModerationScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Posts data
  List<PostModel> _posts = [];
  List<CommentModel> _comments = [];
  bool _isLoadingPosts = false;
  bool _isLoadingComments = false;
  String _searchQuery = '';
  String _currentPostFilter = 'all';
  String _currentCommentFilter = 'all';

  // Statistics
  Map<String, int> _postStats = {
    'total': 0,
    'active': 0,
    'pending': 0,
    'inactive': 0,
  };

  Map<String, int> _commentStats = {
    'total': 0,
    'active': 0,
    'pending': 0,
    'inactive': 0,
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_onTabChanged);
    _loadPostsStatistics();
    _loadCommentsStatistics();
    _loadPosts();
    _loadComments();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      // Clear search when switching tabs
      _searchController.clear();
      _searchQuery = '';
    }
  }

  Future<void> _deletePost(String postId, {List<String>? imageUrls}) async {
    try {
      // Delete post from Firestore
      await PostService.deletePost(postId);
      
      // Delete associated images from storage if they exist
      if (imageUrls != null && imageUrls.isNotEmpty) {
        await Future.wait(imageUrls.map((url) => _deleteImageFromStorage(url)));
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Post deleted successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to delete post: $e')),
        );
      }
    }
  }

  Future<void> _deleteComment(String commentId) async {
    try {
      await CommentService.deleteComment(commentId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Comment deleted successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to delete comment: $e')),
        );
      }
    }
  }

  Future<void> _deleteImageFromStorage(String imageUrl) async {
    try {
      final ref = _storage.refFromURL(imageUrl);
      await ref.delete();
    } catch (e) {
      debugPrint('Error deleting image: $e');
      // Continue even if image deletion fails
    }
  }

  // Load data methods
  Future<void> _loadPosts() async {
    if (_isLoadingPosts) return;

    setState(() {
      _isLoadingPosts = true;
    });

    try {
      final posts = await PostService.getAllPostsPaginated(
        status: _currentPostFilter,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
      );

      if (mounted) {
        setState(() {
          _posts = posts;
          _isLoadingPosts = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingPosts = false;
        });
      }
      print('Error loading posts: $e');
    }
  }

  Future<void> _loadComments() async {
    if (_isLoadingComments) return;

    setState(() {
      _isLoadingComments = true;
    });

    try {
      final comments = await CommentService.getAllCommentsPaginated(
        status: _currentCommentFilter,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
      );

      if (mounted) {
        setState(() {
          _comments = comments;
          _isLoadingComments = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingComments = false;
        });
      }
      print('Error loading comments: $e');
    }
  }

  Future<void> _loadPostsStatistics() async {
    try {
      final stats = await PostService.getPostsStatistics();
      if (mounted) {
        setState(() {
          _postStats = stats;
        });
      }
    } catch (e) {
      print('Error loading post statistics: $e');
    }
  }

  Future<void> _loadCommentsStatistics() async {
    try {
      final stats = await CommentService.getCommentsStatistics();
      if (mounted) {
        setState(() {
          _commentStats = stats;
        });
      }
    } catch (e) {
      print('Error loading comment statistics: $e');
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadPosts();
    _loadComments();
  }

  void _onPostFilterChanged(String filter) {
    setState(() {
      _currentPostFilter = filter;
    });
    _loadPosts();
  }

  void _onCommentFilterChanged(String filter) {
    setState(() {
      _currentCommentFilter = filter;
    });
    _loadComments();
  }

  // Post moderation actions
  Future<void> _approvePost(PostModel post) async {
    try {
      final success = await PostService.updatePostStatus(
        postId: post.id,
        isApproved: true,
        isActive: true,
        moderatorNote: 'Approved by admin',
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Post approved successfully')),
        );
        _loadPosts();
        _loadPostsStatistics();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to approve post: $e')),
        );
      }
    }
  }

  Future<void> _rejectPost(PostModel post) async {
    final TextEditingController noteController = TextEditingController();

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Post'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Are you sure you want to reject this post?'),
            const SizedBox(height: 16),
            TextField(
              controller: noteController,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: 'Enter rejection reason...',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(noteController.text),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      try {
        final success = await PostService.updatePostStatus(
          postId: post.id,
          isApproved: false,
          isActive: false,
          moderatorNote: result,
        );

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Post rejected successfully')),
          );
          _loadPosts();
          _loadPostsStatistics();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to reject post: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      currentRoute: '/moderation',
      child: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          _buildSearchBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPostsTab(),
                _buildCommentsTab(),
                _buildReportsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(
            color: AppConstants.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.shield_outlined,
            size: AppConstants.iconSizeLarge,
            color: AppConstants.primaryColor,
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          const Text(
            'Content Moderation',
            style: TextStyle(
              fontSize: AppConstants.fontSizeHeading,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () {
              _loadPosts();
              _loadComments();
              _loadPostsStatistics();
              _loadCommentsStatistics();
            },
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppConstants.surfaceColor,
      child: TabBar(
        controller: _tabController,
        labelColor: AppConstants.primaryColor,
        unselectedLabelColor: AppConstants.textSecondaryColor,
        indicatorColor: AppConstants.primaryColor,
        tabs: [
          Tab(
            text: 'Posts (${_postStats['total']})',
          ),
          Tab(
            text: 'Comments (${_commentStats['total']})',
          ),
          const Tab(
            text: 'Reports',
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      color: AppConstants.surfaceColor,
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search posts, comments, or users...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    _onSearchChanged('');
                  },
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            borderSide: const BorderSide(color: AppConstants.borderColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            borderSide: const BorderSide(color: AppConstants.primaryColor),
          ),
        ),
        onChanged: _onSearchChanged,
      ),
    );
  }

  Widget _buildPostsTab() {
    return Column(
      children: [
        _buildPostsFilters(),
        Expanded(
          child: _buildPostsList(),
        ),
      ],
    );
  }

  Widget _buildPostsFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(color: AppConstants.borderColor),
        ),
      ),
      child: Row(
        children: [
          const Text(
            'Filter:',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: AppConstants.fontSizeMedium,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('All', 'all', _currentPostFilter),
                  const SizedBox(width: AppConstants.paddingSmall),
                  _buildFilterChip('Active', 'active', _currentPostFilter),
                  const SizedBox(width: AppConstants.paddingSmall),
                  _buildFilterChip('Pending', 'pending', _currentPostFilter),
                  const SizedBox(width: AppConstants.paddingSmall),
                  _buildFilterChip('Inactive', 'inactive', _currentPostFilter),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, String currentFilter) {
    final isSelected = currentFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          _onPostFilterChanged(value);
        }
      },
      selectedColor: AppConstants.primaryColor.withOpacity(0.2),
      checkmarkColor: AppConstants.primaryColor,
    );
  }

  Widget _buildPostsList() {
    if (_isLoadingPosts) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_posts.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.post_add_outlined,
              size: 64,
              color: AppConstants.textHintColor,
            ),
            SizedBox(height: AppConstants.paddingMedium),
            Text(
              'No posts found',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadPosts();
        _loadPostsStatistics();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _posts.length,
        itemBuilder: (context, index) {
          final post = _posts[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
            child: _buildPostItem(post),
          );
        },
      ),
    );
  }

  Widget _buildPostItem(PostModel post) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with user info and status
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: _getPostStatusColor(post).withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppConstants.borderRadiusMedium),
                topRight: Radius.circular(AppConstants.borderRadiusMedium),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: post.authorProfileImageUrl != null
                      ? NetworkImage(post.authorProfileImageUrl!)
                      : null,
                  child: post.authorProfileImageUrl == null
                      ? Text(
                          post.authorName.isNotEmpty ? post.authorName[0].toUpperCase() : 'U',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        )
                      : null,
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.authorName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: AppConstants.fontSizeMedium,
                        ),
                      ),
                      Text(
                        post.timeAgo,
                        style: const TextStyle(
                          color: AppConstants.textSecondaryColor,
                          fontSize: AppConstants.fontSizeSmall,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildPostStatusChip(post),
              ],
            ),
          ),

          // Content
          if (post.content.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Text(
                post.content,
                style: const TextStyle(fontSize: AppConstants.fontSizeMedium),
              ),
            ),

          // Images
          if (post.imageUrls.isNotEmpty)
            Container(
              height: 200,
              margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                child: PageView.builder(
                  itemCount: post.imageUrls.length,
                  itemBuilder: (context, index) {
                    return Image.network(
                      post.imageUrls[index],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: AppConstants.borderColor,
                          child: const Icon(
                            Icons.broken_image,
                            size: 48,
                            color: AppConstants.textHintColor,
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ),

          // Engagement stats
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Row(
              children: [
                Icon(Icons.thumb_up, size: 16, color: AppConstants.textSecondaryColor),
                const SizedBox(width: 4),
                Text('${post.likeCount}', style: const TextStyle(color: AppConstants.textSecondaryColor)),
                const SizedBox(width: 16),
                Icon(Icons.comment, size: 16, color: AppConstants.textSecondaryColor),
                const SizedBox(width: 4),
                Text('${post.commentCount}', style: const TextStyle(color: AppConstants.textSecondaryColor)),
                const SizedBox(width: 16),
                Icon(Icons.share, size: 16, color: AppConstants.textSecondaryColor),
                const SizedBox(width: 4),
                Text('${post.shareCount}', style: const TextStyle(color: AppConstants.textSecondaryColor)),
              ],
            ),
          ),

          // Moderator note
          if (post.moderatorNote != null && post.moderatorNote!.isNotEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: AppConstants.warningColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                border: Border.all(color: AppConstants.warningColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Moderator Note:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.warningColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    post.moderatorNote!,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),

          // Action buttons
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(color: AppConstants.borderColor),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (!post.isApproved)
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _approvePost(post),
                      icon: const Icon(Icons.check, size: 16),
                      label: const Text('Approve'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppConstants.successColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                if (!post.isApproved && post.isActive)
                  const SizedBox(width: AppConstants.paddingSmall),
                if (post.isApproved || post.isActive)
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _rejectPost(post),
                      icon: const Icon(Icons.close, size: 16),
                      label: const Text('Reject'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppConstants.errorColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                const SizedBox(width: AppConstants.paddingSmall),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _deletePost(post.id, imageUrls: post.imageUrls),
                    icon: const Icon(Icons.delete, size: 16),
                    label: const Text('Delete'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.textSecondaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostStatusChip(PostModel post) {
    String status;
    Color color;

    if (!post.isActive) {
      status = 'Inactive';
      color = AppConstants.textSecondaryColor;
    } else if (!post.isApproved) {
      status = 'Pending';
      color = AppConstants.warningColor;
    } else {
      status = 'Active';
      color = AppConstants.successColor;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: color,
          fontSize: AppConstants.fontSizeSmall,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getPostStatusColor(PostModel post) {
    if (!post.isActive) {
      return AppConstants.textSecondaryColor;
    } else if (!post.isApproved) {
      return AppConstants.warningColor;
    } else {
      return AppConstants.successColor;
    }
  }

  Widget _buildCommentsTab() {
    return Column(
      children: [
        _buildCommentsFilters(),
        Expanded(
          child: _buildCommentsList(),
        ),
      ],
    );
  }

  Widget _buildCommentsFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(color: AppConstants.borderColor),
        ),
      ),
      child: Row(
        children: [
          const Text(
            'Filter:',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: AppConstants.fontSizeMedium,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('All', 'all', _currentCommentFilter),
                  const SizedBox(width: AppConstants.paddingSmall),
                  _buildFilterChip('Active', 'active', _currentCommentFilter),
                  const SizedBox(width: AppConstants.paddingSmall),
                  _buildFilterChip('Pending', 'pending', _currentCommentFilter),
                  const SizedBox(width: AppConstants.paddingSmall),
                  _buildFilterChip('Inactive', 'inactive', _currentCommentFilter),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentsList() {
    if (_isLoadingComments) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_comments.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.comment_outlined,
              size: 64,
              color: AppConstants.textHintColor,
            ),
            SizedBox(height: AppConstants.paddingMedium),
            Text(
              'No comments found',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadComments();
        _loadCommentsStatistics();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _comments.length,
        itemBuilder: (context, index) {
          final comment = _comments[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
            child: _buildCommentItem(comment),
          );
        },
      ),
    );
  }

  Widget _buildCommentItem(CommentModel comment) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with user info and status
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: _getCommentStatusColor(comment).withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppConstants.borderRadiusMedium),
                topRight: Radius.circular(AppConstants.borderRadiusMedium),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundImage: comment.authorProfileImageUrl != null
                      ? NetworkImage(comment.authorProfileImageUrl!)
                      : null,
                  child: comment.authorProfileImageUrl == null
                      ? Text(
                          comment.authorName.isNotEmpty ? comment.authorName[0].toUpperCase() : 'U',
                          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                        )
                      : null,
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        comment.authorName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: AppConstants.fontSizeMedium,
                        ),
                      ),
                      Text(
                        comment.timeAgo,
                        style: const TextStyle(
                          color: AppConstants.textSecondaryColor,
                          fontSize: AppConstants.fontSizeSmall,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildCommentStatusChip(comment),
              ],
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Text(
              comment.content,
              style: const TextStyle(fontSize: AppConstants.fontSizeMedium),
            ),
          ),

          // Moderator note
          if (comment.moderatorNote != null && comment.moderatorNote!.isNotEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: AppConstants.warningColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                border: Border.all(color: AppConstants.warningColor.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Moderator Note:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.warningColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    comment.moderatorNote!,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),

          // Action buttons
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(color: AppConstants.borderColor),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (!comment.isApproved)
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _approveComment(comment),
                      icon: const Icon(Icons.check, size: 16),
                      label: const Text('Approve'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppConstants.successColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                if (!comment.isApproved && comment.isActive)
                  const SizedBox(width: AppConstants.paddingSmall),
                if (comment.isApproved || comment.isActive)
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _rejectComment(comment),
                      icon: const Icon(Icons.close, size: 16),
                      label: const Text('Reject'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppConstants.errorColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                const SizedBox(width: AppConstants.paddingSmall),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _deleteComment(comment.id),
                    icon: const Icon(Icons.delete, size: 16),
                    label: const Text('Delete'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.textSecondaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentStatusChip(CommentModel comment) {
    String status;
    Color color;

    if (!comment.isActive) {
      status = 'Inactive';
      color = AppConstants.textSecondaryColor;
    } else if (!comment.isApproved) {
      status = 'Pending';
      color = AppConstants.warningColor;
    } else {
      status = 'Active';
      color = AppConstants.successColor;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: color,
          fontSize: AppConstants.fontSizeSmall,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getCommentStatusColor(CommentModel comment) {
    if (!comment.isActive) {
      return AppConstants.textSecondaryColor;
    } else if (!comment.isApproved) {
      return AppConstants.warningColor;
    } else {
      return AppConstants.successColor;
    }
  }

  // Comment moderation actions
  Future<void> _approveComment(CommentModel comment) async {
    try {
      final success = await CommentService.updateCommentStatus(
        commentId: comment.id,
        isApproved: true,
        isActive: true,
        moderatorNote: 'Approved by admin',
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Comment approved successfully')),
        );
        _loadComments();
        _loadCommentsStatistics();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to approve comment: $e')),
        );
      }
    }
  }

  Future<void> _rejectComment(CommentModel comment) async {
    final TextEditingController noteController = TextEditingController();

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Comment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Are you sure you want to reject this comment?'),
            const SizedBox(height: 16),
            TextField(
              controller: noteController,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: 'Enter rejection reason...',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(noteController.text),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      try {
        final success = await CommentService.updateCommentStatus(
          commentId: comment.id,
          isApproved: false,
          isActive: false,
          moderatorNote: result,
        );

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Comment rejected successfully')),
          );
          _loadComments();
          _loadCommentsStatistics();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to reject comment: $e')),
          );
        }
      }
    }
  }

  Widget _buildReportsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.flag_outlined,
            size: 64,
            color: AppConstants.textHintColor,
          ),
          SizedBox(height: AppConstants.paddingMedium),
          Text(
            'Reports functionality coming soon',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          SizedBox(height: AppConstants.paddingSmall),
          Text(
            'This feature will allow you to review and manage user reports',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textHintColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
