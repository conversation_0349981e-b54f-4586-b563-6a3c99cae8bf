[debug] [2025-07-26T08:40:11.409Z] ----------------------------------------------------------------------
[debug] [2025-07-26T08:40:11.412Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js init firestore
[debug] [2025-07-26T08:40:11.413Z] CLI Version:   13.32.0
[debug] [2025-07-26T08:40:11.413Z] Platform:      win32
[debug] [2025-07-26T08:40:11.413Z] Node Version:  v20.15.1
[debug] [2025-07-26T08:40:11.413Z] Time:          Sat Jul 26 2025 14:40:11 GMT+0600 (Bangladesh Standard Time)
[debug] [2025-07-26T08:40:11.413Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-26T08:40:11.416Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[debug] [2025-07-26T08:40:11.470Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-26T08:40:11.470Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  C:\Users\<USER>\Desktop\amal_app

[debug] [2025-07-26T08:40:12.343Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-07-26T08:40:12.344Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":1615809600000,"node8WarnAfter":1600128000000}
