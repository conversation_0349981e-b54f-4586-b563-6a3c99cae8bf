import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math' as math;
import '../constants/app_constants.dart';
import '../providers/auth_provider.dart';
import '../models/analytics_model.dart';
import '../models/navigation_models.dart';
import '../services/analytics_service.dart';
import '../widgets/dashboard/stats_card.dart';
import '../widgets/dashboard/analytics_chart.dart';
import '../widgets/dashboard/recent_activities.dart';
import '../widgets/responsive_sidebar.dart';
import '../widgets/password_change_dialog.dart';
import 'user_management_screen.dart';
import 'content_management_screen.dart';
import 'product_management_screen.dart';
import 'post_management_screen.dart';
import 'comment_management_screen.dart';
import 'reseller_management_screen.dart';
import 'product_category_management_screen.dart';
import 'analytics_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;
  AnalyticsModel? _analytics;
  bool _isLoading = true;
  bool _isExpanded = false; // Changed to false - sidebar closed by default
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  
  // Sidebar configuration
  final double _sidebarWidth = 260;
  final double _collapsedWidth = 72;
  final int _animationDuration = 300;
  
  // Check if the screen is mobile size
  bool get _isMobile => MediaQuery.of(context).size.width < 768;

  late final List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _loadAnalytics();
    _initializeScreens();
  }

  void _initializeScreens() {
    _screens = [
      _buildDashboardHomeScreen(),
      const UserManagementScreen(),
      const ResellerManagementScreen(),
      const ProductManagementScreen(),
      const ProductCategoryManagementScreen(),
      const PostManagementScreen(),
      const CommentManagementScreen(),
      const ContentManagementScreen(),
      const AnalyticsScreen(),
    ];
  }

  // Navigation groups
  late List<NavigationGroup> _navigationGroups;
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final theme = Theme.of(context);
    
    _navigationGroups = [
      NavigationGroup(
        title: 'MAIN',
        items: [
          NavigationItem(
            icon: Icons.dashboard_outlined,
            activeIcon: Icons.dashboard_rounded,
            label: 'Dashboard',
            tooltip: 'Dashboard',
            index: 0,
          ),
        ],
      ),
      NavigationGroup(
        title: 'MANAGEMENT',
        items: [
          NavigationItem(
            icon: Icons.people_outline_rounded,
            activeIcon: Icons.people_rounded,
            label: 'Users',
            tooltip: 'User Management',
            index: 1,
            badgeCount: 5,
            badgeColor: theme.colorScheme.primary,
          ),
          NavigationItem(
            icon: Icons.store_mall_directory_outlined,
            activeIcon: Icons.store_mall_directory_rounded,
            label: 'Resellers',
            tooltip: 'Reseller Management',
            index: 2,
          ),
          NavigationItem(
            icon: Icons.shopping_bag_outlined,
            activeIcon: Icons.shopping_bag_rounded,
            label: 'Products',
            tooltip: 'Product Management',
            index: 3,
            badgeCount: 3,
            badgeColor: Colors.orange,
          ),
          NavigationItem(
            icon: Icons.category_outlined,
            activeIcon: Icons.category_rounded,
            label: 'Categories',
            tooltip: 'Category Management',
            index: 4,
          ),
          NavigationItem(
            icon: Icons.article_outlined,
            activeIcon: Icons.article_rounded,
            label: 'Posts',
            tooltip: 'Post Management',
            index: 5,
          ),
          NavigationItem(
            icon: Icons.comment_outlined,
            activeIcon: Icons.comment_rounded,
            label: 'Comments',
            tooltip: 'Comment Management',
            index: 6,
          ),
          NavigationItem(
            icon: Icons.content_copy_outlined,
            activeIcon: Icons.content_copy_rounded,
            label: 'Content',
            tooltip: 'Content Management',
            index: 7,
          ),
        ],
      ),
      NavigationGroup(
        title: 'ANALYTICS',
        items: [
          NavigationItem(
            icon: Icons.analytics_outlined,
            activeIcon: Icons.analytics_rounded,
            label: 'Analytics',
            tooltip: 'Analytics Dashboard',
            index: 8,
          ),
          NavigationItem(
            icon: Icons.bar_chart_outlined,
            activeIcon: Icons.bar_chart_rounded,
            label: 'Reports',
            tooltip: 'View Reports',
            index: 7,
          ),
        ],
      ),
      NavigationGroup(
        title: 'SETTINGS',
        items: [
          NavigationItem(
            icon: Icons.help_outline_rounded,
            activeIcon: Icons.help_rounded,
            label: 'Help & Support',
            tooltip: 'Get Help',
            index: 9,
          ),
        ],
      ),
    ];
  }

  // Get all navigation items flattened for the NavigationRail
  List<NavigationRailDestination> get _destinations => _navigationGroups
      .expand((group) => group.items)
      .map(
        (item) => NavigationRailDestination(
          icon: Stack(
            children: [
              Icon(item.icon),
              if (item.badgeCount != null && item.badgeCount! > 0)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '${item.badgeCount! > 9 ? '9+' : item.badgeCount!}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        height: 1,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
          selectedIcon: Icon(item.activeIcon),
          label: Text(
            item.label,
            style: const TextStyle(fontSize: 12),
          ),
          padding: const EdgeInsets.symmetric(vertical: 8),
        ),
      )
      .toList();





  Future<void> _loadAnalytics() async {
    try {
      final analytics = await AnalyticsService.getAnalytics();
      if (mounted) {
        setState(() {
          _analytics = analytics;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load analytics: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  // Toggle sidebar expansion
  void _toggleSidebar() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  // Handle navigation item selection
  void _onItemSelected(int index) {
    print('Navigation item selected: $index, _isExpanded: $_isExpanded');
    setState(() {
      _selectedIndex = index;
      // Auto-close sidebar immediately when navigation item is selected
      _isExpanded = false;
    });
    print('Sidebar closed');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: isDark ? theme.colorScheme.background : Colors.grey[50],
      appBar: _isMobile
          ? AppBar(
              title: const Text('Dashboard'),
              leading: IconButton(
                icon: const Icon(Icons.menu),
                onPressed: _toggleSidebar,
              ),
            )
          : null,
      body: Stack(
        children: [
          // Main content
          AnimatedContainer(
            duration: Duration(milliseconds: _animationDuration),
            margin: EdgeInsets.only(
              left: _isMobile
                  ? 0
                  : (_isExpanded ? _sidebarWidth : _collapsedWidth),
            ),
            child: _screens[_selectedIndex],
          ),
          
          // Sidebar overlay for mobile
          if (_isMobile && _isExpanded)
            GestureDetector(
              onTap: _toggleSidebar,
              child: Container(
                color: Colors.black54,
              ),
            ),
          
          // Sidebar
          if (!_isMobile || _isExpanded)
            AnimatedContainer(
              duration: Duration(milliseconds: _animationDuration),
              width: _isExpanded ? _sidebarWidth : _collapsedWidth,
              child: _buildSidebar(theme),
            ),
          
          // Floating action button for mobile
          if (_isMobile && !_isExpanded)
            Positioned(
              left: 16,
              bottom: 16,
              child: FloatingActionButton(
                heroTag: 'menu_toggle',
                onPressed: _toggleSidebar,
                child: const Icon(Icons.menu),
                backgroundColor: theme.primaryColor,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSidebarFooter(ThemeData theme) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.currentUser;
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: theme.dividerColor.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: InkWell(
        onTap: _isExpanded ? () => _showProfileMenu(context, theme) : null,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(8),
          child: Row(
            children: [
              // User Avatar
              CircleAvatar(
                radius: _isExpanded ? 20 : 16,
                backgroundColor: theme.primaryColor.withOpacity(0.1),
                child: user?.profileImageUrl != null && user!.profileImageUrl!.isNotEmpty
                    ? ClipOval(
                        child: Image.network(
                          user.profileImageUrl!,
                          width: _isExpanded ? 40 : 32,
                          height: _isExpanded ? 40 : 32,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Text(
                              user.email?.isNotEmpty == true ? user.email![0].toUpperCase() : 'A',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: theme.primaryColor,
                                fontWeight: FontWeight.bold,
                                fontSize: _isExpanded ? null : 14,
                              ),
                            );
                          },
                        ),
                      )
                    : Text(
                        user?.email?.isNotEmpty == true ? user!.email![0].toUpperCase() : 'A',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.primaryColor,
                          fontWeight: FontWeight.bold,
                          fontSize: _isExpanded ? null : 14,
                        ),
                      ),
              ),
              if (_isExpanded) ...[
                const SizedBox(width: 12),
                // User Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user?.displayName ?? user?.email ?? 'Admin User',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        'Administrator',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_up,
                  size: 20,
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _showProfileMenu(BuildContext context, ThemeData theme) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.currentUser;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurface.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Profile header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: theme.primaryColor.withOpacity(0.1),
                    child: user?.profileImageUrl != null && user!.profileImageUrl!.isNotEmpty
                        ? ClipOval(
                            child: Image.network(
                              user.profileImageUrl!,
                              width: 60,
                              height: 60,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Text(
                                  user.email?.isNotEmpty == true ? user.email![0].toUpperCase() : 'A',
                                  style: theme.textTheme.headlineSmall?.copyWith(
                                    color: theme.primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                );
                              },
                            ),
                          )
                        : Text(
                            user?.email?.isNotEmpty == true ? user!.email![0].toUpperCase() : 'A',
                            style: theme.textTheme.headlineSmall?.copyWith(
                              color: theme.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user?.displayName ?? user?.email ?? 'Admin User',
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          user?.email ?? '<EMAIL>',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.only(top: 4),
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: theme.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'Administrator',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            // Menu items
            _buildProfileMenuItem(
              context: context,
              theme: theme,
              icon: Icons.person_outline,
              title: 'Profile Settings',
              subtitle: 'Manage your account',
              onTap: () {
                Navigator.pop(context);
                _navigateToProfileSettings();
              },
            ),
            _buildProfileMenuItem(
              context: context,
              theme: theme,
              icon: Icons.security_outlined,
              title: 'Security',
              subtitle: 'Password & authentication',
              onTap: () {
                Navigator.pop(context);
                _navigateToSecurity();
              },
            ),
            _buildProfileMenuItem(
              context: context,
              theme: theme,
              icon: Icons.notifications_outlined,
              title: 'Notifications',
              subtitle: 'Manage notifications',
              onTap: () {
                Navigator.pop(context);
                _navigateToNotifications();
              },
            ),
            _buildProfileMenuItem(
              context: context,
              theme: theme,
              icon: Icons.help_outline,
              title: 'Help & Support',
              subtitle: 'Get help and support',
              onTap: () {
                Navigator.pop(context);
                _navigateToHelp();
              },
            ),
            const Divider(height: 1),
            _buildProfileMenuItem(
              context: context,
              theme: theme,
              icon: Icons.logout,
              title: 'Sign Out',
              subtitle: 'Sign out of your account',
              isDestructive: true,
              onTap: () {
                Navigator.pop(context);
                _handleLogout();
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileMenuItem({
    required BuildContext context,
    required ThemeData theme,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isDestructive
              ? theme.colorScheme.error.withOpacity(0.1)
              : theme.colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: isDestructive ? theme.colorScheme.error : theme.colorScheme.primary,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          color: isDestructive ? theme.colorScheme.error : null,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.onSurface.withOpacity(0.7),
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: theme.colorScheme.onSurface.withOpacity(0.5),
      ),
      onTap: onTap,
    );
  }

  void _navigateToProfileSettings() {
    showDialog(
      context: context,
      builder: (context) => const PasswordChangeDialog(),
    );
  }

  void _navigateToSecurity() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Security Settings - Coming Soon')),
    );
  }

  void _navigateToNotifications() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Notification Settings - Coming Soon')),
    );
  }

  void _navigateToHelp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Help & Support - Coming Soon')),
    );
  }

  void _handleLogout() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    final shouldLogout = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );

    if (shouldLogout == true) {
      try {
        await authProvider.signOut();
        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/login');
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error signing out: $e')),
          );
        }
      }
    }
  }

  // Build sidebar widget
  Widget _buildSidebar(ThemeData theme) {
    return Container(
      width: _isExpanded ? _sidebarWidth : _collapsedWidth,
      color: theme.colorScheme.surface,
      child: Column(
        children: [
          // Sidebar header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: theme.dividerColor.withOpacity(0.1),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: theme.primaryColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.admin_panel_settings,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                if (_isExpanded) ...[
                  const SizedBox(width: 12),
                  Text(
                    'Admin Panel',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),
          // Navigation items
          Expanded(
            child: ListView.builder(
              itemCount: _navigationGroups.length,
              itemBuilder: (context, index) {
                final group = _navigationGroups[index];
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (group.title.isNotEmpty && _isExpanded)
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                        child: Text(
                          group.title,
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.textTheme.bodySmall?.color,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 1.0,
                          ),
                        ),
                      ),
                    ...group.items.map((item) {
                      final isSelected = _selectedIndex == item.index;
                      return AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? theme.colorScheme.primary.withOpacity(0.1)
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(8),
                            onTap: () => _onItemSelected(item.index),
                            child: ListTile(
                              leading: Icon(
                                isSelected ? item.activeIcon : item.icon,
                                color: isSelected
                                    ? theme.colorScheme.primary
                                    : theme.iconTheme.color?.withOpacity(0.7),
                              ),
                        title: _isExpanded
                            ? Text(
                                item.label,
                                style: theme.textTheme.titleSmall?.copyWith(
                                  color: isSelected
                                      ? theme.colorScheme.primary
                                      : null,
                                  fontWeight:
                                      isSelected ? FontWeight.w600 : null,
                                ),
                              )
                            : null,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 4,
                              ),
                              dense: true,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                    if (index < _navigationGroups.length - 1)
                      const Divider(height: 8, thickness: 1),
                  ],
                );
              },
            ),
          ),
          // Sidebar footer
          _buildSidebarFooter(theme),
        ],
      ),
    );
  }

  // Dashboard home screen content
  Widget _buildDashboardHomeScreen() {
    return Scaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            padding: EdgeInsets.symmetric(
              horizontal: constraints.maxWidth > 600 ? 24.0 : 16.0,
              vertical: 16.0,
            ),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: constraints.maxHeight - 32, // Account for padding
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildWelcomeSection(),
                  const SizedBox(height: 24),
                  _buildStatsCards(),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;
        String getInitials() {
          if (user?.displayName?.isNotEmpty == true) {
            return user!.displayName![0].toUpperCase();
          } else if (user?.email?.isNotEmpty == true) {
            return user!.email[0].toUpperCase();
          }
          return 'A';
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  child: Text(
                    getInitials(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Welcome back, ${authProvider.currentUser?.displayNameOrUsername ?? 'Admin'}!',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      Text(
                        'Here\'s what\'s happening with your platform today.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatsCards() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate the number of columns based on screen width
        final crossAxisCount = constraints.maxWidth > 1200 
            ? 4 
            : constraints.maxWidth > 800 
                ? 2 
                : 1;
                
        return GridView.count(
          crossAxisCount: crossAxisCount,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: constraints.maxWidth < 600 ? 2.5 : 1.5,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          children: [
            StatsCard(
              title: 'Total Users',
              value: _analytics?.totalUsers.toString() ?? '0',
              icon: Icons.people,
              color: AppConstants.primaryColor,
              growth: _analytics?.userGrowthRate,
            ),
            StatsCard(
              title: 'Total Posts',
              value: _analytics?.totalPosts.toString() ?? '0',
              icon: Icons.post_add,
              color: AppConstants.successColor,
              growth: _analytics?.postGrowthRate,
            ),
            StatsCard(
              title: 'Total Products',
              value: _analytics?.totalProducts.toString() ?? '0',
              icon: Icons.shopping_bag,
              color: AppConstants.warningColor,
              growth: _analytics?.productGrowthRate,
            ),
            StatsCard(
              title: 'Pending Applications',
              value: _analytics?.pendingResellerApplications.toString() ?? '0',
              icon: Icons.pending_actions,
              color: AppConstants.errorColor,
            ),
          ],
        );
      },
    );
  }
}
