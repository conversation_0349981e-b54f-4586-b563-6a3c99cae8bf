enum UserRole {
  user('user', 'User'),
  reseller('reseller', 'Reseller'),
  admin('admin', 'Admin');

  const UserRole(this.value, this.displayName);

  final String value;
  final String displayName;

  static UserRole fromString(String value) {
    switch (value.toLowerCase()) {
      case 'reseller':
        return UserRole.reseller;
      case 'admin':
        return UserRole.admin;
      case 'user':
      default:
        return UserRole.user;
    }
  }

  bool get isUser => this == UserRole.user;
  bool get isReseller => this == UserRole.reseller;
  bool get isAdmin => this == UserRole.admin;
  
  bool get canAccessProductDashboard => isReseller || isAdmin;
  bool get canUploadProducts => isReseller || isAdmin;
  bool get canManageUsers => isAdmin;
  bool get canApproveResellers => isAdmin;
}

enum ResellerApplicationStatus {
  none('none', 'No Application'),
  pending('pending', 'Pending'),
  approved('approved', 'Approved'),
  rejected('rejected', 'Rejected');

  const ResellerApplicationStatus(this.value, this.displayName);

  final String value;
  final String displayName;

  static ResellerApplicationStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'pending':
        return ResellerApplicationStatus.pending;
      case 'approved':
        return ResellerApplicationStatus.approved;
      case 'rejected':
        return ResellerApplicationStatus.rejected;
      case 'none':
      default:
        return ResellerApplicationStatus.none;
    }
  }

  bool get isPending => this == ResellerApplicationStatus.pending;
  bool get isApproved => this == ResellerApplicationStatus.approved;
  bool get isRejected => this == ResellerApplicationStatus.rejected;
  bool get isNone => this == ResellerApplicationStatus.none;
}

enum RegistrationStatus {
  pending('pending', 'Pending Approval'),
  approved('approved', 'Approved'),
  rejected('rejected', 'Rejected');

  const RegistrationStatus(this.value, this.displayName);

  final String value;
  final String displayName;

  static RegistrationStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'approved':
        return RegistrationStatus.approved;
      case 'rejected':
        return RegistrationStatus.rejected;
      case 'pending':
      default:
        return RegistrationStatus.pending;
    }
  }

  bool get isPending => this == RegistrationStatus.pending;
  bool get isApproved => this == RegistrationStatus.approved;
  bool get isRejected => this == RegistrationStatus.rejected;
}
