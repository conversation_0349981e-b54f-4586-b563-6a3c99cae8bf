import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../constants/app_constants.dart';
import '../models/product_model.dart';
import '../services/product_service.dart';
import 'edit_product_screen.dart';

class ProductDetailScreen extends StatefulWidget {
  final ProductModel product;

  const ProductDetailScreen({
    super.key,
    required this.product,
  });

  @override
  State<ProductDetailScreen> createState() => _ProductDetailScreenState();
}

class _ProductDetailScreenState extends State<ProductDetailScreen> {
  late ProductModel _product;
  int _currentImageIndex = 0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _product = widget.product;
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppConstants.successColor,
        ),
      );
    }
  }

  Future<void> _approveProduct() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await ProductService.approveProduct(
        productId: _product.id,
        moderatorNote: 'Approved by admin',
      );

      if (success) {
        setState(() {
          _product = _product.copyWith(
            isApproved: true,
            moderatorNote: 'Approved by admin',
            updatedAt: DateTime.now(),
          );
        });
        _showSuccessSnackBar('Product approved successfully');
      } else {
        throw Exception('Failed to approve product');
      }
    } catch (e) {
      _showErrorSnackBar('Error approving product: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _rejectProduct() async {
    final TextEditingController noteController = TextEditingController();
    
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Product'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for rejection:'),
            const SizedBox(height: AppConstants.paddingMedium),
            TextField(
              controller: noteController,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: 'Enter rejection reason...',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(noteController.text),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      setState(() {
        _isLoading = true;
      });

      try {
        final success = await ProductService.rejectProduct(
          productId: _product.id,
          moderatorNote: result,
        );

        if (success) {
          setState(() {
            _product = _product.copyWith(
              isApproved: false,
              moderatorNote: result,
              updatedAt: DateTime.now(),
            );
          });
          _showSuccessSnackBar('Product rejected');
        } else {
          throw Exception('Failed to reject product');
        }
      } catch (e) {
        _showErrorSnackBar('Error rejecting product: $e');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteProduct() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Product'),
        content: const Text('Are you sure you want to delete this product? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        final success = await ProductService.deleteProduct(_product.id);

        if (success) {
          if (mounted) {
            Navigator.of(context).pop(true); // Return true to indicate deletion
            _showSuccessSnackBar('Product deleted successfully');
          }
        } else {
          throw Exception('Failed to delete product');
        }
      } catch (e) {
        _showErrorSnackBar('Error deleting product: $e');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _editProduct() async {
    final result = await Navigator.of(context).push<ProductModel>(
      MaterialPageRoute(
        builder: (context) => EditProductScreen(product: _product),
      ),
    );

    if (result != null) {
      setState(() {
        _product = result;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('Product Details'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _editProduct();
                    break;
                  case 'approve':
                    _approveProduct();
                    break;
                  case 'reject':
                    _rejectProduct();
                    break;
                  case 'delete':
                    _deleteProduct();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, color: AppConstants.primaryColor),
                      SizedBox(width: 8),
                      Text('Edit'),
                    ],
                  ),
                ),
                if (!_product.isApproved)
                  const PopupMenuItem(
                    value: 'approve',
                    child: Row(
                      children: [
                        Icon(Icons.check, color: AppConstants.successColor),
                        SizedBox(width: 8),
                        Text('Approve'),
                      ],
                    ),
                  ),
                if (_product.isApproved)
                  const PopupMenuItem(
                    value: 'reject',
                    child: Row(
                      children: [
                        Icon(Icons.close, color: AppConstants.warningColor),
                        SizedBox(width: 8),
                        Text('Reject'),
                      ],
                    ),
                  ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: AppConstants.errorColor),
                      SizedBox(width: 8),
                      Text('Delete'),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image Gallery
            _buildImageGallery(),

            // Product Information
            Padding(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status Badges
                  _buildStatusBadges(),

                  const SizedBox(height: AppConstants.paddingMedium),

                  // Product Name
                  Text(
                    _product.name,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeXLarge,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: AppConstants.paddingSmall),

                  // Price
                  _buildPriceSection(),

                  const SizedBox(height: AppConstants.paddingMedium),

                  // Category and Stock
                  _buildCategoryAndStock(),

                  const SizedBox(height: AppConstants.paddingMedium),

                  // Description
                  _buildDescriptionSection(),

                  const SizedBox(height: AppConstants.paddingMedium),

                  // Tags
                  if (_product.tags.isNotEmpty) _buildTagsSection(),

                  const SizedBox(height: AppConstants.paddingMedium),

                  // Seller Information
                  _buildSellerSection(),

                  const SizedBox(height: AppConstants.paddingMedium),

                  // Product Metadata
                  _buildMetadataSection(),

                  // Moderator Note
                  if (_product.moderatorNote != null && _product.moderatorNote!.isNotEmpty)
                    _buildModeratorNoteSection(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageGallery() {
    if (_product.imageUrls.isEmpty) {
      return Container(
        height: 300,
        color: AppConstants.backgroundColor,
        child: const Center(
          child: Icon(
            Icons.image_not_supported,
            size: 64,
            color: AppConstants.textSecondaryColor,
          ),
        ),
      );
    }

    return Column(
      children: [
        // Main Image
        Container(
          height: 300,
          width: double.infinity,
          child: PageView.builder(
            itemCount: _product.imageUrls.length,
            onPageChanged: (index) {
              setState(() {
                _currentImageIndex = index;
              });
            },
            itemBuilder: (context, index) {
              return CachedNetworkImage(
                imageUrl: _product.imageUrls[index],
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppConstants.backgroundColor,
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppConstants.backgroundColor,
                  child: const Icon(
                    Icons.error,
                    color: AppConstants.errorColor,
                    size: 64,
                  ),
                ),
              );
            },
          ),
        ),

        // Image Indicators
        if (_product.imageUrls.length > 1)
          Container(
            padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _product.imageUrls.asMap().entries.map((entry) {
                return Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentImageIndex == entry.key
                        ? AppConstants.primaryColor
                        : AppConstants.borderColor,
                  ),
                );
              }).toList(),
            ),
          ),
      ],
    );
  }

  Widget _buildStatusBadges() {
    return Wrap(
      spacing: AppConstants.paddingSmall,
      runSpacing: AppConstants.paddingSmall,
      children: [
        _buildStatusBadge(
          'Available',
          _product.isAvailable,
          _product.isAvailable ? AppConstants.successColor : AppConstants.errorColor,
        ),
        _buildStatusBadge(
          'Featured',
          _product.isFeatured,
          _product.isFeatured ? AppConstants.primaryColor : AppConstants.borderColor,
        ),
        _buildStatusBadge(
          'Approved',
          _product.isApproved,
          _product.isApproved ? AppConstants.successColor : AppConstants.warningColor,
        ),
        if (_product.isDeleted)
          _buildStatusBadge(
            'Deleted',
            true,
            AppConstants.errorColor,
          ),
      ],
    );
  }

  Widget _buildStatusBadge(String label, bool isActive, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: isActive ? color : AppConstants.borderColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: isActive ? Colors.white : AppConstants.textSecondaryColor,
          fontSize: AppConstants.fontSizeSmall,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildPriceSection() {
    return Row(
      children: [
        Text(
          '৳${_product.price.toStringAsFixed(2)}',
          style: const TextStyle(
            fontSize: AppConstants.fontSizeXLarge,
            fontWeight: FontWeight.bold,
            color: AppConstants.primaryColor,
          ),
        ),
        if (_product.originalPrice != null && _product.originalPrice! > _product.price) ...[
          const SizedBox(width: AppConstants.paddingSmall),
          Text(
            '৳${_product.originalPrice!.toStringAsFixed(2)}',
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              decoration: TextDecoration.lineThrough,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(width: AppConstants.paddingSmall),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingSmall,
              vertical: 2,
            ),
            decoration: BoxDecoration(
              color: AppConstants.successColor,
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
            ),
            child: Text(
              '${((_product.originalPrice! - _product.price) / _product.originalPrice! * 100).round()}% OFF',
              style: const TextStyle(
                color: Colors.white,
                fontSize: AppConstants.fontSizeSmall,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCategoryAndStock() {
    return Row(
      children: [
        Expanded(
          child: _buildInfoCard('Category', _product.category),
        ),
        const SizedBox(width: AppConstants.paddingMedium),
        Expanded(
          child: _buildInfoCard(
            'Stock',
            _product.stockQuantity > 999999 ? 'Unlimited' : _product.stockQuantity.toString(),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoCard(String label, String value) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(color: AppConstants.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Description',
          style: TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            border: Border.all(color: AppConstants.borderColor),
          ),
          child: Text(
            _product.description,
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTagsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tags',
          style: TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Wrap(
          spacing: AppConstants.paddingSmall,
          runSpacing: AppConstants.paddingSmall,
          children: _product.tags.map((tag) {
            return Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
                vertical: AppConstants.paddingSmall,
              ),
              decoration: BoxDecoration(
                color: AppConstants.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                border: Border.all(color: AppConstants.primaryColor.withOpacity(0.3)),
              ),
              child: Text(
                tag,
                style: const TextStyle(
                  color: AppConstants.primaryColor,
                  fontSize: AppConstants.fontSizeSmall,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSellerSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(color: AppConstants.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Seller Information',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          _buildSellerInfoRow('Name', _product.sellerName),
          _buildSellerInfoRow('ID', _product.sellerId),
          if (_product.sellerWhatsApp != null)
            _buildSellerInfoRow('WhatsApp', _product.sellerWhatsApp!),
          if (_product.sellerWeChat != null)
            _buildSellerInfoRow('WeChat', _product.sellerWeChat!),
          if (_product.sellerLocation != null)
            _buildSellerInfoRow('Location', _product.sellerLocation!),
        ],
      ),
    );
  }

  Widget _buildSellerInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetadataSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(color: AppConstants.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Product Metadata',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          _buildMetadataRow('Product ID', _product.id),
          _buildMetadataRow('Created', _formatDateTime(_product.createdAt)),
          _buildMetadataRow('Updated', _formatDateTime(_product.updatedAt)),
          if (_product.deletedAt != null)
            _buildMetadataRow('Deleted', _formatDateTime(_product.deletedAt!)),
        ],
      ),
    );
  }

  Widget _buildMetadataRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModeratorNoteSection() {
    return Container(
      margin: const EdgeInsets.only(top: AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.warningColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(color: AppConstants.warningColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Moderator Note',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.bold,
              color: AppConstants.warningColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            _product.moderatorNote!,
            style: const TextStyle(
              fontSize: AppConstants.fontSizeSmall,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
